{"extension": {"name": "Roo Code", "description": "에디터 내에서 동작하는 AI 개발팀 전체입니다."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "안녕하세요, {{name}}님! {{count}}개의 알림이 있습니다.", "items": {"zero": "항목 없음", "one": "1개 항목", "other": "{{count}}개 항목"}, "confirmation": {"reset_state": "확장 프로그램의 모든 상태와 보안 저장소를 재설정하시겠습니까? 이 작업은 취소할 수 없습니다.", "delete_config_profile": "이 구성 프로필을 삭제하시겠습니까?", "delete_custom_mode": "이 사용자 지정 모드를 삭제하시겠습니까?", "delete_message": "무엇을 삭제하시겠습니까?", "just_this_message": "이 메시지만", "this_and_subsequent": "이 메시지와 모든 후속 메시지"}, "errors": {"invalid_data_uri": "잘못된 데이터 URI 형식", "error_copying_image": "이미지 복사 중 오류 발생: {{errorMessage}}", "error_saving_image": "이미지 저장 중 오류 발생: {{errorMessage}}", "error_opening_image": "이미지 열기 중 오류 발생: {{error}}", "could_not_open_file": "파일을 열 수 없습니다: {{errorMessage}}", "could_not_open_file_generic": "파일을 열 수 없습니다!", "checkpoint_timeout": "체크포인트 복원을 시도하는 중 시간 초과되었습니다.", "checkpoint_failed": "체크포인트 복원에 실패했습니다.", "no_workspace": "먼저 프로젝트 폴더를 열어주세요", "update_support_prompt": "지원 프롬프트 업데이트에 실패했습니다", "reset_support_prompt": "지원 프롬프트 재설정에 실패했습니다", "enhance_prompt": "프롬프트 향상에 실패했습니다", "get_system_prompt": "시스템 프롬프트 가져오기에 실패했습니다", "search_commits": "커밋 검색에 실패했습니다", "save_api_config": "API 구성 저장에 실패했습니다", "create_api_config": "API 구성 생성에 실패했습니다", "rename_api_config": "API 구성 이름 변경에 실패했습니다", "load_api_config": "API 구성 로드에 실패했습니다", "delete_api_config": "API 구성 삭제에 실패했습니다", "list_api_config": "API 구성 목록 가져오기에 실패했습니다", "update_server_timeout": "서버 타임아웃 업데이트에 실패했습니다", "hmr_not_running": "로컬 개발 서버가 실행되고 있지 않아 HMR이 작동하지 않습니다. HMR을 활성화하려면 확장 프로그램을 실행하기 전에 'npm run dev'를 실행하세요.", "retrieve_current_mode": "상태에서 현재 모드를 검색하는 데 오류가 발생했습니다.", "failed_delete_repo": "관련 shadow 저장소 또는 브랜치 삭제 실패: {{error}}", "failed_remove_directory": "작업 디렉토리 제거 실패: {{error}}", "custom_storage_path_unusable": "사용자 지정 저장 경로 \"{{path}}\"를 사용할 수 없어 기본 경로를 사용합니다", "cannot_access_path": "경로 {{path}}에 접근할 수 없습니다: {{error}}", "settings_import_failed": "설정 가져오기 실패: {{error}}.", "mistake_limit_guidance": "이는 모델의 사고 과정 실패나 도구를 제대로 사용하지 못하는 것을 나타낼 수 있으며, 사용자 가이드를 통해 완화할 수 있습니다 (예: \"작업을 더 작은 단계로 나누어 시도해보세요\").", "violated_organization_allowlist": "작업 실행 실패: 현재 프로필이 조직 설정을 위반합니다", "condense_failed": "컨텍스트 압축에 실패했습니다", "condense_not_enough_messages": "컨텍스트를 압축할 메시지가 충분하지 않습니다", "condensed_recently": "컨텍스트가 최근 압축되었습니다; 이 시도를 건너뜁니다", "condense_handler_invalid": "컨텍스트 압축을 위한 API 핸들러가 유효하지 않습니다", "condense_context_grew": "압축 중 컨텍스트 크기가 증가했습니다; 이 시도를 건너뜁니다", "share_task_failed": "작업 공유에 실패했습니다", "share_no_active_task": "공유할 활성 작업이 없습니다", "share_auth_required": "인증이 필요합니다. 작업을 공유하려면 로그인하세요.", "share_not_enabled": "이 조직에서는 작업 공유가 활성화되지 않았습니다.", "share_task_not_found": "작업을 찾을 수 없거나 액세스가 거부되었습니다.", "claudeCode": {"processExited": "Claude Code 프로세스가 코드 {{exitCode}}로 종료되었습니다.", "errorOutput": "오류 출력: {{output}}", "processExitedWithError": "Claude Code 프로세스가 코드 {{exitCode}}로 종료되었습니다. 오류 출력: {{output}}", "stoppedWithReason": "Claude Code가 다음 이유로 중지되었습니다: {{reason}}", "apiKeyModelPlanMismatch": "API 키와 구독 플랜에서 다른 모델을 허용합니다. 선택한 모델이 플랜에 포함되어 있는지 확인하세요."}, "geminiCli": {"oauthLoadFailed": "OAuth 자격 증명을 로드하지 못했습니다. 먼저 인증하세요: {{error}}", "tokenRefreshFailed": "OAuth 토큰을 새로 고치지 못했습니다: {{error}}", "onboardingTimeout": "온보딩 작업이 60초 후 시간 초과되었습니다. 나중에 다시 시도하세요.", "projectDiscoveryFailed": "프로젝트 ID를 찾을 수 없습니다. 'gemini auth'로 인증되었는지 확인하세요.", "rateLimitExceeded": "속도 제한을 초과했습니다. 무료 등급 제한에 도달했습니다.", "badRequest": "잘못된 요청: {{details}}", "apiError": "Gemini CLI API 오류: {{error}}", "completionError": "Gemini CLI 완성 오류: {{error}}"}}, "warnings": {"no_terminal_content": "선택된 터미널 내용이 없습니다", "missing_task_files": "이 작업의 파일이 누락되었습니다. 작업 목록에서 제거하시겠습니까?"}, "info": {"no_changes": "변경 사항이 없습니다.", "clipboard_copy": "시스템 프롬프트가 클립보드에 성공적으로 복사되었습니다", "history_cleanup": "이력에서 파일이 누락된 {{count}}개의 작업을 정리했습니다.", "custom_storage_path_set": "사용자 지정 저장 경로 설정됨: {{path}}", "default_storage_path": "기본 저장 경로로 되돌아갔습니다", "settings_imported": "설정이 성공적으로 가져와졌습니다.", "share_link_copied": "공유 링크가 클립보드에 복사되었습니다", "image_copied_to_clipboard": "이미지 데이터 URI가 클립보드에 복사되었습니다", "image_saved": "이미지가 {{path}}에 저장되었습니다", "organization_share_link_copied": "조직 공유 링크가 클립보드에 복사되었습니다!", "public_share_link_copied": "공개 공유 링크가 클립보드에 복사되었습니다!"}, "answers": {"yes": "예", "no": "아니오", "cancel": "취소", "remove": "제거", "keep": "유지"}, "tasks": {"canceled": "작업 오류: 사용자에 의해 중지 및 취소되었습니다.", "deleted": "작업 실패: 사용자에 의해 중지 및 삭제되었습니다.", "incomplete": "작업 #{{taskNumber}} (미완료)", "no_messages": "작업 #{{taskNumber}} (메시지 없음)"}, "storage": {"prompt_custom_path": "대화 내역을 위한 사용자 지정 저장 경로를 입력하세요. 기본 위치를 사용하려면 비워두세요", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "절대 경로를 입력하세요 (예: D:\\RooCodeStorage 또는 /home/<USER>/storage)", "enter_valid_path": "유효한 경로를 입력하세요"}, "input": {"task_prompt": "Roo에게 무엇을 시킬까요?", "task_placeholder": "여기에 작업을 입력하세요"}, "settings": {"providers": {"groqApiKey": "Groq API 키", "getGroqApiKey": "Groq API 키 받기", "claudeCode": {"pathLabel": "Claude Code 경로", "description": "Claude Code CLI의 선택적 경로입니다. 설정되지 않은 경우 기본값은 'claude'입니다.", "placeholder": "기본값: claude"}}}, "customModes": {"errors": {"yamlParseError": ".roomodes 파일의 {{line}}번째 줄에서 유효하지 않은 YAML입니다. 다음을 확인하세요:\n• 올바른 들여쓰기 (탭이 아닌 공백 사용)\n• 일치하는 따옴표와 괄호\n• 유효한 YAML 구문", "schemaValidationError": ".roomodes의 사용자 정의 모드 형식이 유효하지 않습니다:\n{{issues}}", "invalidFormat": "사용자 정의 모드 형식이 유효하지 않습니다. 설정이 올바른 YAML 형식을 따르는지 확인하세요.", "updateFailed": "사용자 정의 모드 업데이트 실패: {{error}}", "deleteFailed": "사용자 정의 모드 삭제 실패: {{error}}", "resetFailed": "사용자 정의 모드 재설정 실패: {{error}}", "modeNotFound": "쓰기 오류: 모드를 찾을 수 없습니다", "noWorkspaceForProject": "프로젝트별 모드용 작업 공간 폴더를 찾을 수 없습니다"}}, "mdm": {"errors": {"cloud_auth_required": "조직에서 Roo Code Cloud 인증이 필요합니다. 계속하려면 로그인하세요.", "organization_mismatch": "조직의 Roo Code Cloud 계정으로 인증해야 합니다.", "verification_failed": "조직 인증을 확인할 수 없습니다."}}}