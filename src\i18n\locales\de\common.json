{"extension": {"name": "Roo Code", "description": "Ein komplettes Entwicklerteam mit KI in deinem Editor."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "<PERSON><PERSON><PERSON><PERSON>, {{name}}! Du hast {{count}} Benachrichtigungen.", "items": {"zero": "<PERSON><PERSON>", "one": "Ein Element", "other": "{{count}} El<PERSON>e"}, "confirmation": {"reset_state": "Möchtest du wirklich alle Zustände und geheimen Speicher in der Erweiterung zurücksetzen? Dies kann nicht rückgängig gemacht werden.", "delete_config_profile": "Möchtest du dieses Konfigurationsprofil wirklich löschen?", "delete_custom_mode": "Möchtest du diesen benutzerdefinierten Modus wirklich löschen?", "delete_message": "Was möchtest du löschen?", "just_this_message": "<PERSON><PERSON> dies<PERSON> Nach<PERSON>", "this_and_subsequent": "Diese und alle nachfolgenden Nachrichten"}, "errors": {"invalid_data_uri": "Ungültiges Daten-URI-Format", "error_copying_image": "<PERSON><PERSON> beim Ko<PERSON>ren des Bildes: {{errorMessage}}", "error_saving_image": "<PERSON><PERSON> beim Speichern des Bildes: {{errorMessage}}", "error_opening_image": "<PERSON><PERSON> beim Ö<PERSON>nen des Bildes: {{error}}", "could_not_open_file": "<PERSON>i konnte nicht geöffnet werden: {{errorMessage}}", "could_not_open_file_generic": "Datei konnte nicht geöffnet werden!", "checkpoint_timeout": "Zeitüberschreitung beim Versuch, den Checkpoint wiederherzustellen.", "checkpoint_failed": "Fehler beim Wiederherstellen des Checkpoints.", "no_workspace": "Bitte öffne zu<PERSON>t einen Projektordner", "update_support_prompt": "Fehler beim Aktualisieren der Support-Nachricht", "reset_support_prompt": "Fehler beim Zurücksetzen der Support-Nachricht", "enhance_prompt": "Fehler beim Verbessern der Nachricht", "get_system_prompt": "Fehler beim Abrufen der Systemnachricht", "search_commits": "<PERSON><PERSON> beim <PERSON>", "save_api_config": "Fehler beim Speichern der API-Konfiguration", "create_api_config": "Fehler beim Erstellen der API-Konfiguration", "rename_api_config": "Fehler beim Umbenennen der API-Konfiguration", "load_api_config": "Fehler beim Laden der API-Konfiguration", "delete_api_config": "Fehler beim Löschen der API-Konfiguration", "list_api_config": "Fehler beim Abrufen der API-Konfigurationsliste", "update_server_timeout": "Fehler beim Aktualisieren des Server-Timeouts", "hmr_not_running": "Der lokale Entwicklungsserver läuft nicht, HMR wird nicht funktionieren. Bitte führen Sie 'npm run dev' vor dem Start der Erweiterung aus, um HMR zu aktivieren.", "retrieve_current_mode": "Fehler beim Abrufen des aktuellen Modus aus dem Zustand.", "failed_delete_repo": "Fehler beim Löschen des zugehörigen Shadow-Repositorys oder -Zweigs: {{error}}", "failed_remove_directory": "Fehler beim Entfernen des Aufgabenverzeichnisses: {{error}}", "custom_storage_path_unusable": "Benutzerdefinierter Speicherpfad \"{{path}}\" ist nicht verwendbar, Standardpfad wird verwendet", "cannot_access_path": "Zugriff auf Pfad {{path}} nicht möglich: {{error}}", "settings_import_failed": "<PERSON><PERSON> beim Importieren der Einstellungen: {{error}}.", "mistake_limit_guidance": "Dies kann auf einen Fehler im Denkprozess des Modells oder die Unfähigkeit hin<PERSON>sen, ein <PERSON><PERSON> richtig zu verwenden, was durch Benutzerführung behoben werden kann (z.B. \"Versuche, die Aufgabe in kleinere Schritte zu unterteilen\").", "violated_organization_allowlist": "Aufgabe konnte nicht ausgeführt werden: Das aktuelle Profil verstößt gegen die Einstellungen deiner Organisation", "condense_failed": "Fehler beim Verdichten des Kontexts", "condense_not_enough_messages": "Nicht genügend Nachrichten zum Verdichten des Kontexts", "condensed_recently": "Kontext wurde kürzlich verdichtet; dieser Versuch wird übersprungen", "condense_handler_invalid": "API-Handler zum Verdichten des Kontexts ist ungültig", "condense_context_grew": "Kontextgröße ist während der Verdichtung gewachsen; dieser Versuch wird übersprungen", "share_task_failed": "Teilen der Aufgabe fehlgeschlagen. Bitte versuche es erneut.", "share_no_active_task": "Keine aktive Aufgabe zum Teilen", "share_auth_required": "Authentifizierung erforderlich. Bitte melde dich an, um Aufgaben zu teilen.", "share_not_enabled": "Aufgabenfreigabe ist für diese Organisation nicht aktiviert.", "share_task_not_found": "Aufgabe nicht gefunden oder Zugriff verweigert.", "claudeCode": {"processExited": "Claude Code Prozess wurde mit Code {{exitCode}} beendet.", "errorOutput": "Fehlerausgabe: {{output}}", "processExitedWithError": "<PERSON> Code Prozess wurde mit Code {{exitCode}} beendet. Fehlerausgabe: {{output}}", "stoppedWithReason": "<PERSON> wurde mit Grund gestoppt: {{reason}}", "apiKeyModelPlanMismatch": "API-Schlüssel und Abonnement-Pläne erlauben verschiedene Modelle. <PERSON><PERSON> sic<PERSON>, dass das ausgewählte Modell in deinem Plan enthalten ist."}, "geminiCli": {"oauthLoadFailed": "Fehler beim Laden der OAuth-Anmeldedaten. Bitte authentifiziere dich zuerst: {{error}}", "tokenRefreshFailed": "Fehler beim Aktualisieren des OAuth-Tokens: {{error}}", "onboardingTimeout": "Onboarding-Vorgang nach 60 Sekunden abgebrochen. Bitte versuche es später erneut.", "projectDiscoveryFailed": "Projekt-ID konnte nicht ermittelt werden. <PERSON><PERSON> sic<PERSON>, dass du mit 'gemini auth' authentifiziert bist.", "rateLimitExceeded": "Anfragenlimit überschritten. Die Limits des kostenlosen Tarifs wurden erreicht.", "badRequest": "Ungültige Anfrage: {{details}}", "apiError": "Gemini CLI API-<PERSON>hler: {{error}}", "completionError": "Gemini CLI Vervollständigungsfehler: {{error}}"}}, "warnings": {"no_terminal_content": "Kein Terminal-Inhalt ausgewählt", "missing_task_files": "Die Dateien dieser Aufgabe fehlen. Möchtest du sie aus der Aufgabenliste entfernen?"}, "info": {"no_changes": "Keine Änderungen gefunden.", "clipboard_copy": "Systemnachricht erfolgreich in die Zwischenablage kopiert", "history_cleanup": "{{count}} Aufgabe(n) mit fehlenden Dateien aus dem Verlauf bereinigt.", "custom_storage_path_set": "Benutzerdefinierter Speicherpfad festgelegt: {{path}}", "default_storage_path": "Auf Standardspeicherpfad zurückgesetzt", "settings_imported": "Einstellungen erfolgreich importiert.", "share_link_copied": "Share-Link in die Zwischenablage kopiert", "image_copied_to_clipboard": "Bild-Daten-URI in die Zwischenablage kopiert", "image_saved": "Bild ges<PERSON> unter {{path}}", "organization_share_link_copied": "Organisations-Freigabelink in die Zwischenablage kopiert!", "public_share_link_copied": "Öffentlicher Freigabelink in die Zwischenablage kopiert!"}, "answers": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>", "cancel": "Abbrechen", "remove": "Entfernen", "keep": "Behalten"}, "tasks": {"canceled": "Aufgabenfehler: Die Aufgabe wurde vom Benutzer gestoppt und abgebrochen.", "deleted": "Aufgabenfehler: Die Aufgabe wurde vom Benutzer gestoppt und gelöscht.", "incomplete": "Aufgabe #{{taskNumber}} (Unvollständig)", "no_messages": "Aufgabe #{{taskNumber}} (<PERSON><PERSON>)"}, "storage": {"prompt_custom_path": "Gib den benutzerdefinierten Speicherpfad für den Gesprächsverlauf ein, leer lassen für Standardspeicherort", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "Bitte gib einen absoluten Pfad ein (z.B. D:\\RooCodeStorage oder /home/<USER>/storage)", "enter_valid_path": "Bitte gib einen gültigen Pfad ein"}, "input": {"task_prompt": "Was soll Roo tun?", "task_placeholder": "Gib deine Aufgabe hier ein"}, "settings": {"providers": {"groqApiKey": "Groq API-Schlüssel", "getGroqApiKey": "Groq API-Schlüssel erhalten", "claudeCode": {"pathLabel": "Claude Code Pfad", "description": "Optionaler Pfad zu deiner Claude Code CLI. Standardmäßig 'claude', falls nicht festgelegt.", "placeholder": "Standard: claude"}}}, "customModes": {"errors": {"yamlParseError": "Ungültiges YAML in .roomodes-Datei in Zeile {{line}}. Bitte überprüfe:\n• Korrekte Einrückung (verwende Leerzeichen, keine <PERSON>bs)\n• Passende Anführungszeichen und Klammern\n• Gültige YAML-Syntax", "schemaValidationError": "Ungültiges Format für benutzerdefinierte Modi in .roomodes:\n{{issues}}", "invalidFormat": "Ungültiges Format für benutzerdefinierte Modi. <PERSON><PERSON> stelle sicher, dass deine Einstellungen dem korrekten YAML-Format folgen.", "updateFailed": "Fehler beim Aktualisieren des benutzerdefinierten Modus: {{error}}", "deleteFailed": "<PERSON><PERSON> beim Löschen des benutzerdefinierten Modus: {{error}}", "resetFailed": "<PERSON><PERSON> beim Zurücksetzen der benutzerdefinierten Modi: {{error}}", "modeNotFound": "Schreibfehler: Modus nicht gefunden", "noWorkspaceForProject": "<PERSON>in Arbeitsbereich-Ordner für projektspezifischen Modus gefunden"}}, "mdm": {"errors": {"cloud_auth_required": "Deine Organisation erfordert eine Roo Code Cloud-Authentifizierung. Bitte melde dich an, um fortzufahren.", "organization_mismatch": "Du musst mit dem Roo Code Cloud-Konto deiner Organisation authentifiziert sein.", "verification_failed": "Die Organisationsauthentifizierung konnte nicht verifiziert werden."}}}