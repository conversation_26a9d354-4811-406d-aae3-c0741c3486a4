{"root": ["./src/app.tsx", "./src/index.tsx", "./src/types.d.ts", "./src/vite-env.d.ts", "./src/__tests__/app.spec.tsx", "./src/__tests__/contextwindowprogress.spec.tsx", "./src/__tests__/contextwindowprogresslogic.spec.ts", "./src/__tests__/errorboundary.spec.tsx", "./src/__tests__/searchableselect.spec.tsx", "./src/__tests__/telemetryclient.spec.ts", "./src/__tests__/command-autocomplete.spec.ts", "./src/components/errorboundary.tsx", "./src/components/__tests__/errorboundary.spec.tsx", "./src/components/account/accountview.tsx", "./src/components/account/__tests__/accountview.spec.tsx", "./src/components/chat/announcement.tsx", "./src/components/chat/apiconfigselector.tsx", "./src/components/chat/autoapprovemenu.tsx", "./src/components/chat/autoapprovedrequestlimitwarning.tsx", "./src/components/chat/batchdiffapproval.tsx", "./src/components/chat/batchfilepermission.tsx", "./src/components/chat/browsersessionrow.tsx", "./src/components/chat/chatrow.tsx", "./src/components/chat/chattextarea.tsx", "./src/components/chat/chatview.tsx", "./src/components/chat/checkpointwarning.tsx", "./src/components/chat/codeindexpopover.tsx", "./src/components/chat/codebasesearchresult.tsx", "./src/components/chat/codebasesearchresultsdisplay.tsx", "./src/components/chat/commandexecution.tsx", "./src/components/chat/commandexecutionerror.tsx", "./src/components/chat/commandpatternselector.tsx", "./src/components/chat/contextcondenserow.tsx", "./src/components/chat/contextmenu.tsx", "./src/components/chat/contextwindowprogress.tsx", "./src/components/chat/editmodecontrols.tsx", "./src/components/chat/followupsuggest.tsx", "./src/components/chat/iconbutton.tsx", "./src/components/chat/indexingstatusbadge.tsx", "./src/components/chat/markdown.tsx", "./src/components/chat/mcpexecution.tsx", "./src/components/chat/mention.tsx", "./src/components/chat/messagemodificationconfirmationdialog.tsx", "./src/components/chat/modeselector.tsx", "./src/components/chat/profileviolationwarning.tsx", "./src/components/chat/progressindicator.tsx", "./src/components/chat/queuedmessages.tsx", "./src/components/chat/reasoningblock.tsx", "./src/components/chat/sharebutton.tsx", "./src/components/chat/slashcommanditem.tsx", "./src/components/chat/slashcommandslist.tsx", "./src/components/chat/slashcommandspopover.tsx", "./src/components/chat/systempromptwarning.tsx", "./src/components/chat/taskactions.tsx", "./src/components/chat/taskheader.tsx", "./src/components/chat/todolistdisplay.tsx", "./src/components/chat/updatetodolisttoolblock.tsx", "./src/components/chat/__tests__/announcement.spec.tsx", "./src/components/chat/__tests__/apiconfigselector.spec.tsx", "./src/components/chat/__tests__/autoapprovemenu.spec.tsx", "./src/components/chat/__tests__/batchfilepermission.spec.tsx", "./src/components/chat/__tests__/chattextarea.spec.tsx", "./src/components/chat/__tests__/chatview.auto-approve-new.spec.tsx", "./src/components/chat/__tests__/chatview.auto-approve.spec.tsx", "./src/components/chat/__tests__/chatview.keyboard-fix.spec.tsx", "./src/components/chat/__tests__/chatview.spec.tsx", "./src/components/chat/__tests__/commandexecution.spec.tsx", "./src/components/chat/__tests__/commandpatternselector.spec.tsx", "./src/components/chat/__tests__/editmodecontrols.spec.tsx", "./src/components/chat/__tests__/followupsuggest.spec.tsx", "./src/components/chat/__tests__/indexingstatusbadge.spec.tsx", "./src/components/chat/__tests__/modeselector.spec.tsx", "./src/components/chat/__tests__/sharebutton.spec.tsx", "./src/components/chat/__tests__/taskactions.spec.tsx", "./src/components/chat/__tests__/taskheader.spec.tsx", "./src/components/chat/checkpoints/checkpointmenu.tsx", "./src/components/chat/checkpoints/checkpointsaved.tsx", "./src/components/chat/checkpoints/schema.ts", "./src/components/chat/hooks/useprompthistory.ts", "./src/components/common/codeaccordian.tsx", "./src/components/common/codeblock.tsx", "./src/components/common/iconbutton.tsx", "./src/components/common/markdownblock.tsx", "./src/components/common/mermaidactionbuttons.tsx", "./src/components/common/mermaidblock.tsx", "./src/components/common/mermaidbutton.tsx", "./src/components/common/modal.tsx", "./src/components/common/tab.tsx", "./src/components/common/tabbutton.tsx", "./src/components/common/telemetrybanner.tsx", "./src/components/common/thumbnails.tsx", "./src/components/common/tooluseblock.tsx", "./src/components/common/vscodebuttonlink.tsx", "./src/components/common/versionindicator.tsx", "./src/components/common/zoomcontrols.tsx", "./src/components/common/__mocks__/codeblock.tsx", "./src/components/common/__mocks__/markdownblock.tsx", "./src/components/common/__tests__/codeblock.spec.tsx", "./src/components/common/__tests__/markdownblock.spec.tsx", "./src/components/history/batchdeletetaskdialog.tsx", "./src/components/history/copybutton.tsx", "./src/components/history/deletebutton.tsx", "./src/components/history/deletetaskdialog.tsx", "./src/components/history/exportbutton.tsx", "./src/components/history/historypreview.tsx", "./src/components/history/historyview.tsx", "./src/components/history/taskitem.tsx", "./src/components/history/taskitemfooter.tsx", "./src/components/history/taskitemheader.tsx", "./src/components/history/usetasksearch.ts", "./src/components/history/__tests__/batchdeletetaskdialog.spec.tsx", "./src/components/history/__tests__/copybutton.spec.tsx", "./src/components/history/__tests__/deletebutton.spec.tsx", "./src/components/history/__tests__/deletetaskdialog.spec.tsx", "./src/components/history/__tests__/exportbutton.spec.tsx", "./src/components/history/__tests__/historypreview.spec.tsx", "./src/components/history/__tests__/historyview.spec.tsx", "./src/components/history/__tests__/taskitem.spec.tsx", "./src/components/history/__tests__/taskitemfooter.spec.tsx", "./src/components/history/__tests__/taskitemheader.spec.tsx", "./src/components/history/__tests__/usetasksearch.spec.tsx", "./src/components/human-relay/humanrelaydialog.tsx", "./src/components/marketplace/issuefooter.tsx", "./src/components/marketplace/marketplacelistview.tsx", "./src/components/marketplace/marketplaceview.tsx", "./src/components/marketplace/marketplaceviewstatemanager.ts", "./src/components/marketplace/usestatemanager.ts", "./src/components/marketplace/__tests__/marketplacelistview.spec.tsx", "./src/components/marketplace/__tests__/marketplaceview.spec.tsx", "./src/components/marketplace/__tests__/marketplaceviewstatemanager.spec.ts", "./src/components/marketplace/components/marketplaceinstallmodal.tsx", "./src/components/marketplace/components/marketplaceitemcard.tsx", "./src/components/marketplace/components/__tests__/marketplaceinstallmodal-optional-params.spec.tsx", "./src/components/marketplace/components/__tests__/marketplaceinstallmodal.spec.tsx", "./src/components/marketplace/components/__tests__/marketplaceitemcard.spec.tsx", "./src/components/mcp/mcpenabledtoggle.tsx", "./src/components/mcp/mcperrorrow.tsx", "./src/components/mcp/mcpresourcerow.tsx", "./src/components/mcp/mcptoolrow.tsx", "./src/components/mcp/mcpview.tsx", "./src/components/mcp/__tests__/mcptoolrow.spec.tsx", "./src/components/modes/deletemodedialog.tsx", "./src/components/modes/modesview.tsx", "./src/components/modes/__tests__/modesview.spec.tsx", "./src/components/settings/about.tsx", "./src/components/settings/apiconfigmanager.tsx", "./src/components/settings/apierrormessage.tsx", "./src/components/settings/apioptions.tsx", "./src/components/settings/autoapprovesettings.tsx", "./src/components/settings/autoapprovetoggle.tsx", "./src/components/settings/browsersettings.tsx", "./src/components/settings/checkpointsettings.tsx", "./src/components/settings/consecutivemistakelimitcontrol.tsx", "./src/components/settings/contextmanagementsettings.tsx", "./src/components/settings/diffsettingscontrol.tsx", "./src/components/settings/experimentalfeature.tsx", "./src/components/settings/experimentalsettings.tsx", "./src/components/settings/languagesettings.tsx", "./src/components/settings/modeldescriptionmarkdown.tsx", "./src/components/settings/modelinfoview.tsx", "./src/components/settings/modelpicker.tsx", "./src/components/settings/notificationsettings.tsx", "./src/components/settings/promptssettings.tsx", "./src/components/settings/r1formatsetting.tsx", "./src/components/settings/ratelimitsecondscontrol.tsx", "./src/components/settings/section.tsx", "./src/components/settings/sectionheader.tsx", "./src/components/settings/settingsview.tsx", "./src/components/settings/temperaturecontrol.tsx", "./src/components/settings/terminalsettings.tsx", "./src/components/settings/thinkingbudget.tsx", "./src/components/settings/todolistsettingscontrol.tsx", "./src/components/settings/constants.ts", "./src/components/settings/styles.ts", "./src/components/settings/transforms.ts", "./src/components/settings/types.ts", "./src/components/settings/__tests__/apiconfigmanager.spec.tsx", "./src/components/settings/__tests__/apioptions.spec.tsx", "./src/components/settings/__tests__/autoapprovetoggle.spec.tsx", "./src/components/settings/__tests__/contextmanagementsettings.spec.tsx", "./src/components/settings/__tests__/modelpicker.spec.tsx", "./src/components/settings/__tests__/settingsview.spec.tsx", "./src/components/settings/__tests__/temperaturecontrol.spec.tsx", "./src/components/settings/__tests__/thinkingbudget.spec.tsx", "./src/components/settings/__tests__/todolistsettingscontrol.spec.tsx", "./src/components/settings/providers/anthropic.tsx", "./src/components/settings/providers/bedrock.tsx", "./src/components/settings/providers/bedrockcustomarn.tsx", "./src/components/settings/providers/chutes.tsx", "./src/components/settings/providers/claudecode.tsx", "./src/components/settings/providers/deepseek.tsx", "./src/components/settings/providers/gemini.tsx", "./src/components/settings/providers/geminicli.tsx", "./src/components/settings/providers/glama.tsx", "./src/components/settings/providers/groq.tsx", "./src/components/settings/providers/huggingface.tsx", "./src/components/settings/providers/lmstudio.tsx", "./src/components/settings/providers/litellm.tsx", "./src/components/settings/providers/mistral.tsx", "./src/components/settings/providers/moonshot.tsx", "./src/components/settings/providers/ollama.tsx", "./src/components/settings/providers/openai.tsx", "./src/components/settings/providers/openaicompatible.tsx", "./src/components/settings/providers/openrouter.tsx", "./src/components/settings/providers/openrouterbalancedisplay.tsx", "./src/components/settings/providers/requesty.tsx", "./src/components/settings/providers/requestybalancedisplay.tsx", "./src/components/settings/providers/unbound.tsx", "./src/components/settings/providers/vscodelm.tsx", "./src/components/settings/providers/vertex.tsx", "./src/components/settings/providers/xai.tsx", "./src/components/settings/providers/index.ts", "./src/components/settings/providers/__tests__/bedrock.spec.tsx", "./src/components/settings/providers/__tests__/gemini.spec.tsx", "./src/components/settings/providers/__tests__/geminicli.spec.tsx", "./src/components/settings/providers/__tests__/huggingface.spec.tsx", "./src/components/settings/providers/__tests__/openaicompatible.spec.tsx", "./src/components/settings/providers/__tests__/vertex.spec.tsx", "./src/components/settings/utils/headers.ts", "./src/components/settings/utils/organizationfilters.ts", "./src/components/settings/utils/__tests__/headers.test.ts", "./src/components/settings/utils/__tests__/organizationfilters.test.ts", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/autosize-textarea.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/button.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/index.ts", "./src/components/ui/input.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/searchable-select.tsx", "./src/components/ui/select-dropdown.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/standard-tooltip.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toggle-switch.tsx", "./src/components/ui/tooltip.tsx", "./src/components/ui/__tests__/select-dropdown.spec.tsx", "./src/components/ui/__tests__/toggle-switch.spec.tsx", "./src/components/ui/__tests__/tooltip.spec.tsx", "./src/components/ui/hooks/index.ts", "./src/components/ui/hooks/useclipboard.ts", "./src/components/ui/hooks/usenoninteractiveclick.ts", "./src/components/ui/hooks/useopenrouterkeyinfo.ts", "./src/components/ui/hooks/useopenroutermodelproviders.ts", "./src/components/ui/hooks/userequestykeyinfo.ts", "./src/components/ui/hooks/userooportal.ts", "./src/components/ui/hooks/useroutermodels.ts", "./src/components/ui/hooks/useselectedmodel.ts", "./src/components/ui/hooks/__tests__/useselectedmodel.spec.ts", "./src/components/welcome/roocloudcta.tsx", "./src/components/welcome/roohero.tsx", "./src/components/welcome/rootips.tsx", "./src/components/welcome/welcomeview.tsx", "./src/components/welcome/__tests__/rootips.spec.tsx", "./src/context/extensionstatecontext.tsx", "./src/context/__tests__/extensionstatecontext.spec.tsx", "./src/hooks/useautoapprovalstate.ts", "./src/hooks/useautoapprovaltoggles.ts", "./src/hooks/useescapekey.spec.ts", "./src/hooks/useescapekey.ts", "./src/hooks/usetooltip.ts", "./src/hooks/__tests__/useautoapprovalstate.spec.ts", "./src/i18n/translationcontext.tsx", "./src/i18n/setup.ts", "./src/i18n/__mocks__/translationcontext.tsx", "./src/i18n/__tests__/translationcontext.spec.tsx", "./src/lib/utils.ts", "./src/oauth/urls.ts", "./src/utils/telemetryclient.ts", "./src/utils/clipboard.ts", "./src/utils/command-parser.ts", "./src/utils/command-validation.ts", "./src/utils/context-mentions.ts", "./src/utils/doclinks.ts", "./src/utils/format.ts", "./src/utils/formatprice.ts", "./src/utils/getlanguagefrompath.ts", "./src/utils/highlight.ts", "./src/utils/highlighter.ts", "./src/utils/imageutils.ts", "./src/utils/mcp.ts", "./src/utils/model-utils.ts", "./src/utils/path-mentions.ts", "./src/utils/removeleadingnonalphanumeric.ts", "./src/utils/sourcemapinitializer.ts", "./src/utils/sourcemaputils.ts", "./src/utils/test-utils.tsx", "./src/utils/textmatetohljs.ts", "./src/utils/url.ts", "./src/utils/usedebounceeffect.ts", "./src/utils/validate.ts", "./src/utils/vscode.ts", "./src/utils/__tests__/telemetryclient.spec.ts", "./src/utils/__tests__/command-parser.spec.ts", "./src/utils/__tests__/command-validation.spec.ts", "./src/utils/__tests__/context-mentions.spec.ts", "./src/utils/__tests__/format.spec.ts", "./src/utils/__tests__/model-utils.spec.ts", "./src/utils/__tests__/path-mentions.test.ts", "./src/utils/__tests__/sourcemaputils.spec.ts", "./src/utils/__tests__/validate.test.ts", "./src/vite-plugins/sourcemapplugin.ts", "../src/shared/extensionmessage.ts", "../src/shared/profilevalidator.ts", "../src/shared/telemetrysetting.ts", "../src/shared/webviewmessage.ts", "../src/shared/api.ts", "../src/shared/array.ts", "../src/shared/checkexistapiconfig.ts", "../src/shared/combineapirequests.ts", "../src/shared/combinecommandsequences.ts", "../src/shared/context-mentions.ts", "../src/shared/cost.ts", "../src/shared/embeddingmodels.ts", "../src/shared/experiments.ts", "../src/shared/getapimetrics.ts", "../src/shared/globalfilenames.ts", "../src/shared/language.ts", "../src/shared/mcp.ts", "../src/shared/modes.ts", "../src/shared/package.ts", "../src/shared/safejsonparse.ts", "../src/shared/support-prompt.ts", "../src/shared/todo.ts", "../src/shared/tools.ts", "../src/shared/vscodeselectorutils.ts", "../src/shared/__tests__/profilevalidator.spec.ts", "../src/shared/__tests__/api.spec.ts", "../src/shared/__tests__/checkexistapiconfig.spec.ts", "../src/shared/__tests__/combineapirequests.spec.ts", "../src/shared/__tests__/combinecommandsequences.spec.ts", "../src/shared/__tests__/context-mentions.spec.ts", "../src/shared/__tests__/experiments-preventfocusdisruption.spec.ts", "../src/shared/__tests__/experiments.spec.ts", "../src/shared/__tests__/getapimetrics.spec.ts", "../src/shared/__tests__/language.spec.ts", "../src/shared/__tests__/modes-empty-prompt-component.spec.ts", "../src/shared/__tests__/modes.spec.ts", "../src/shared/__tests__/support-prompts.spec.ts", "../src/shared/__tests__/vscodeselectorutils.spec.ts", "./vitest.setup.ts"], "errors": true, "version": "5.8.3"}